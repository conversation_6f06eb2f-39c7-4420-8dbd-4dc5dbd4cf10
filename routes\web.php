<?php

use Illuminate\Support\Facades\Route;
use Livewire\Volt\Volt;

Route::get('/', function () {
    return view('welcome');
})->name('home');

Route::view('dashboard', 'dashboard')
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Volt::route('settings/profile', 'settings.profile')->name('settings.profile');
    Volt::route('settings/password', 'settings.password')->name('settings.password');
    Volt::route('settings/appearance', 'settings.appearance')->name('settings.appearance');

    // Student Management Routes
    Route::resource('students', App\Http\Controllers\StudentController::class);

    // Additional Student Routes
    Route::post('students/bulk-import', [App\Http\Controllers\StudentController::class, 'bulkImport'])
         ->name('students.bulk-import');

    Route::post('students/generate-qr-codes', [App\Http\Controllers\StudentController::class, 'generateQRCodes'])
         ->name('students.generate-qr-codes');

    Route::get('students/export/attendance', [App\Http\Controllers\StudentController::class, 'exportAttendance'])
         ->name('students.export-attendance');

    Route::get('students/analytics', [App\Http\Controllers\StudentController::class, 'getAnalytics'])
         ->name('students.analytics');

    // Attendance Management Routes
    Route::prefix('attendance')->name('attendance.')->group(function () {
        // QR Code Scanning
        Route::post('scan-qr', [App\Http\Controllers\AttendanceController::class, 'scanQR'])
             ->name('scan-qr');

        // Manual Attendance Marking
        Route::post('mark', [App\Http\Controllers\AttendanceController::class, 'markAttendance'])
             ->name('mark');

        // Class Attendance View
        Route::get('class', [App\Http\Controllers\AttendanceController::class, 'getClassAttendance'])
             ->name('class');

        // Update Attendance Record
        Route::put('{attendance}', [App\Http\Controllers\AttendanceController::class, 'updateAttendance'])
             ->name('update');

        // Generate Reports
        Route::post('reports/generate', [App\Http\Controllers\AttendanceController::class, 'generateReport'])
             ->name('reports.generate');

        // Analytics
        Route::get('analytics', [App\Http\Controllers\AttendanceController::class, 'getAnalytics'])
             ->name('analytics');

        // SMS Notifications
        Route::post('sms/send', [App\Http\Controllers\AttendanceController::class, 'sendSMSNotification'])
             ->name('sms.send');

        // Bulk Operations
        Route::post('bulk', [App\Http\Controllers\AttendanceController::class, 'bulkAttendance'])
             ->name('bulk');

        // Student History
        Route::get('history', [App\Http\Controllers\AttendanceController::class, 'getAttendanceHistory'])
             ->name('history');

        // Export Data
        Route::post('export', [App\Http\Controllers\AttendanceController::class, 'exportReport'])
             ->name('export');
    });
});

require __DIR__.'/auth.php';
