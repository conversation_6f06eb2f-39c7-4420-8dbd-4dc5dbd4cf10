<?php

use Illuminate\Support\Facades\Route;
use Livewire\Volt\Volt;

Route::get('/', function () {
    return view('welcome');
})->name('home');

Route::view('dashboard', 'dashboard')
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Volt::route('settings/profile', 'settings.profile')->name('settings.profile');
    Volt::route('settings/password', 'settings.password')->name('settings.password');
    Volt::route('settings/appearance', 'settings.appearance')->name('settings.appearance');

    // Student Management Routes
    Route::resource('students', App\Http\Controllers\StudentController::class);

    // Additional Student Routes
    Route::post('students/bulk-import', [App\Http\Controllers\StudentController::class, 'bulkImport'])
         ->name('students.bulk-import');

    Route::post('students/generate-qr-codes', [App\Http\Controllers\StudentController::class, 'generateQRCodes'])
         ->name('students.generate-qr-codes');

    Route::get('students/export/attendance', [App\Http\Controllers\StudentController::class, 'exportAttendance'])
         ->name('students.export-attendance');

    Route::get('students/analytics', [App\Http\Controllers\StudentController::class, 'getAnalytics'])
         ->name('students.analytics');
});

require __DIR__.'/auth.php';
