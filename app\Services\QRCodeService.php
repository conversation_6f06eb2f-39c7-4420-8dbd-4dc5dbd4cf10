<?php

namespace App\Services;

use App\Models\Student;
use Endroid\QrCode\Builder\Builder;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\Label\LabelAlignment;
use Endroid\QrCode\Logo\Logo;
use Endroid\QrCode\RoundBlockSizeMode;
use Endroid\QrCode\Writer\PngWriter;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class QRCodeService
{
    /**
     * Generate QR code for a single student.
     */
    public function generateForStudent(Student $student): string
    {
        $qrData = json_encode([
            'student_id' => $student->student_id,
            'name' => $student->full_name,
            'grade_level' => $student->grade_level,
            'section' => $student->section,
            'hash' => $student->qr_code_hash,
        ]);

        $result = Builder::create()
            ->writer(new PngWriter())
            ->writerOptions([])
            ->data($qrData)
            ->encoding(new Encoding('UTF-8'))
            ->errorCorrectionLevel(ErrorCorrectionLevel::High)
            ->size(300)
            ->margin(10)
            ->roundBlockSizeMode(RoundBlockSizeMode::Margin)
            ->labelText($student->full_name)
            ->labelAlignment(LabelAlignment::Center)
            ->validateResult(false)
            ->build();

        $filename = 'qr-codes/student-' . $student->student_id . '-' . Str::random(8) . '.png';
        Storage::disk('public')->put($filename, $result->getString());

        return $filename;
    }

    /**
     * Generate QR codes for multiple students.
     */
    public function generateBatch(array $studentIds): array
    {
        $students = Student::whereIn('id', $studentIds)->get();
        $results = [];

        foreach ($students as $student) {
            try {
                $filename = $this->generateForStudent($student);
                $results[] = [
                    'student_id' => $student->student_id,
                    'name' => $student->full_name,
                    'qr_code_path' => $filename,
                    'status' => 'success',
                ];
            } catch (\Exception $e) {
                $results[] = [
                    'student_id' => $student->student_id,
                    'name' => $student->full_name,
                    'qr_code_path' => null,
                    'status' => 'error',
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }

    /**
     * Generate QR codes for all students in a grade/section.
     */
    public function generateForGradeSection(?string $gradeLevel = null, ?string $section = null): array
    {
        $query = Student::active();

        if ($gradeLevel) {
            $query->byGradeLevel($gradeLevel);
        }

        if ($section) {
            $query->bySection($section);
        }

        $students = $query->get();
        $studentIds = $students->pluck('id')->toArray();

        return $this->generateBatch($studentIds);
    }

    /**
     * Regenerate QR code for a student (when data changes).
     */
    public function regenerateForStudent(Student $student): string
    {
        // Delete old QR code if exists
        if ($student->qr_code_path && Storage::disk('public')->exists($student->qr_code_path)) {
            Storage::disk('public')->delete($student->qr_code_path);
        }

        // Generate new QR code hash
        $student->generateQRCode();
        $student->save();

        // Generate new QR code image
        $filename = $this->generateForStudent($student);

        // Update student record with new QR code path
        $student->update(['qr_code_path' => $filename]);

        return $filename;
    }

    /**
     * Validate QR code data.
     */
    public function validateQRData(string $qrData): array
    {
        try {
            $data = json_decode($qrData, true);

            if (!$data || !isset($data['student_id'], $data['hash'])) {
                return ['valid' => false, 'error' => 'Invalid QR code format'];
            }

            $student = Student::where('student_id', $data['student_id'])->first();

            if (!$student) {
                return ['valid' => false, 'error' => 'Student not found'];
            }

            // Verify hash matches
            if ($student->qr_code_hash !== $data['hash']) {
                return ['valid' => false, 'error' => 'QR code hash mismatch'];
            }

            return [
                'valid' => true,
                'student' => $student,
                'data' => $data,
            ];
        } catch (\Exception $e) {
            return ['valid' => false, 'error' => 'Failed to decode QR code: ' . $e->getMessage()];
        }
    }
}
