<?php

namespace App\Http\Controllers;

use App\Enums\AttendanceStatus;
use App\Events\AttendanceRecorded;
use App\Models\Attendance;
use App\Models\Student;
use App\Models\Teacher;
use App\Models\Subject;
use App\Services\AttendanceReportService;
use App\Services\AttendanceExportService;
use App\Services\SmsService;
use App\Services\QRCodeService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\View\View;

class AttendanceController extends Controller
{
    protected AttendanceReportService $reportService;
    protected AttendanceExportService $exportService;
    protected SmsService $smsService;
    protected QRCodeService $qrService;

    public function __construct(
        AttendanceReportService $reportService,
        AttendanceExportService $exportService,
        SmsService $smsService,
        QRCodeService $qrService
    ) {
        $this->reportService = $reportService;
        $this->exportService = $exportService;
        $this->smsService = $smsService;
        $this->qrService = $qrService;
    }

    /**
     * Process QR code scan, validate student, and record attendance.
     */
    public function scanQR(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'qr_data' => ['required', 'string'],
                'teacher_id' => ['required', 'integer', 'exists:teachers,id'],
                'subject_id' => ['required', 'integer', 'exists:subjects,id'],
                'latitude' => ['nullable', 'numeric', 'between:-90,90'],
                'longitude' => ['nullable', 'numeric', 'between:-180,180'],
                'device_info' => ['nullable', 'array'],
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            // Validate QR code and get student
            $qrValidation = $this->qrService->validateQRData($request->qr_data);
            
            if (!$qrValidation['valid']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid QR code',
                    'error' => $qrValidation['error'],
                ], 400);
            }

            $student = Student::find($qrValidation['student_id']);
            
            if (!$student || $student->status->value !== 'active') {
                return response()->json([
                    'success' => false,
                    'message' => 'Student not found or inactive',
                ], 404);
            }

            // Check if attendance already exists for today
            $existingAttendance = Attendance::where('student_id', $student->id)
                ->where('subject_id', $request->subject_id)
                ->where('date', today()->format('Y-m-d'))
                ->first();

            if ($existingAttendance) {
                // Update time_out if already present
                if ($existingAttendance->status === AttendanceStatus::PRESENT && !$existingAttendance->time_out) {
                    $existingAttendance->markTimeOut();

                    return response()->json([
                        'success' => true,
                        'message' => 'Time out recorded successfully',
                        'data' => [
                            'attendance' => $existingAttendance->fresh(),
                            'student' => $student,
                            'action' => 'time_out',
                        ],
                    ]);
                }

                return response()->json([
                    'success' => false,
                    'message' => 'Attendance already recorded for today',
                    'data' => [
                        'attendance' => $existingAttendance,
                        'student' => $student,
                    ],
                ], 409);
            }

            // Create new attendance record
            $timeIn = now()->format('H:i:s');
            $isLate = $this->isStudentLate($timeIn);
            
            $attendance = Attendance::create([
                'student_id' => $student->id,
                'teacher_id' => $request->teacher_id,
                'subject_id' => $request->subject_id,
                'date' => today(),
                'time_in' => $timeIn,
                'status' => $isLate ? AttendanceStatus::LATE : AttendanceStatus::PRESENT,
                'ip_address' => $request->ip(),
                'device_info' => array_merge($request->device_info ?? [], [
                    'latitude' => $request->latitude,
                    'longitude' => $request->longitude,
                    'user_agent' => $request->userAgent(),
                    'scan_method' => 'qr_code',
                ]),
            ]);

            // Broadcast real-time update
            $this->broadcastAttendanceUpdate($attendance);

            // Cache for offline sync
            $this->cacheAttendanceForOfflineSync($attendance);

            return response()->json([
                'success' => true,
                'message' => $isLate ? 'Late attendance recorded' : 'Attendance recorded successfully',
                'data' => [
                    'attendance' => $attendance->load(['student', 'teacher', 'subject']),
                    'student' => $student,
                    'is_late' => $isLate,
                    'action' => 'time_in',
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('QR scan attendance failed', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process QR scan',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Manual attendance marking with validation.
     */
    public function markAttendance(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'student_id' => ['required', 'integer', 'exists:students,id'],
                'teacher_id' => ['required', 'integer', 'exists:teachers,id'],
                'subject_id' => ['required', 'integer', 'exists:subjects,id'],
                'date' => ['required', 'date', 'before_or_equal:today'],
                'status' => ['required', 'in:' . implode(',', AttendanceStatus::values())],
                'time_in' => ['nullable', 'date_format:H:i:s'],
                'time_out' => ['nullable', 'date_format:H:i:s', 'after:time_in'],
                'remarks' => ['nullable', 'string', 'max:1000'],
                'bulk_mode' => ['nullable', 'boolean'],
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $student = Student::find($request->student_id);
            
            if ($student->status->value !== 'active') {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot mark attendance for inactive student',
                ], 400);
            }

            // Check for existing attendance
            $existingAttendance = Attendance::where('student_id', $request->student_id)
                ->where('subject_id', $request->subject_id)
                ->where('date', $request->date)
                ->first();

            if ($existingAttendance && !$request->boolean('bulk_mode')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Attendance already exists for this date',
                    'data' => ['attendance' => $existingAttendance],
                ], 409);
            }

            DB::beginTransaction();

            if ($existingAttendance) {
                // Update existing attendance
                $existingAttendance->update([
                    'status' => AttendanceStatus::from($request->status),
                    'time_in' => $request->time_in,
                    'time_out' => $request->time_out,
                    'remarks' => $request->remarks,
                    'teacher_id' => $request->teacher_id,
                ]);
                
                $attendance = $existingAttendance;
                $action = 'updated';
            } else {
                // Create new attendance
                $attendance = Attendance::create([
                    'student_id' => $request->student_id,
                    'teacher_id' => $request->teacher_id,
                    'subject_id' => $request->subject_id,
                    'date' => $request->date,
                    'time_in' => $request->time_in,
                    'time_out' => $request->time_out,
                    'status' => AttendanceStatus::from($request->status),
                    'remarks' => $request->remarks,
                    'ip_address' => $request->ip(),
                    'device_info' => [
                        'user_agent' => $request->userAgent(),
                        'scan_method' => 'manual',
                        'marked_by' => Auth::id(),
                    ],
                ]);
                
                $action = 'created';
            }

            DB::commit();

            // Broadcast real-time update
            $this->broadcastAttendanceUpdate($attendance);

            // Cache for offline sync
            $this->cacheAttendanceForOfflineSync($attendance);

            return response()->json([
                'success' => true,
                'message' => "Attendance {$action} successfully",
                'data' => [
                    'attendance' => $attendance->load(['student', 'teacher', 'subject']),
                    'action' => $action,
                ],
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Manual attendance marking failed', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to mark attendance',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get today's attendance for specific class/section.
     */
    public function getClassAttendance(Request $request): JsonResponse|View|RedirectResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'grade_level' => ['required', 'in:11,12'],
                'section' => ['required', 'string', 'max:50'],
                'subject_id' => ['required', 'integer', 'exists:subjects,id'],
                'date' => ['nullable', 'date'],
                'teacher_id' => ['nullable', 'integer', 'exists:teachers,id'],
            ]);

            if ($validator->fails()) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Validation failed',
                        'errors' => $validator->errors(),
                    ], 422);
                }

                return back()->withErrors($validator)->withInput();
            }

            $date = $request->date ? Carbon::parse($request->date) : today();

            // Get all students in the class
            $students = Student::byGradeLevel($request->grade_level)
                ->bySection($request->section)
                ->active()
                ->with(['attendance' => function ($query) use ($request, $date) {
                    $query->where('subject_id', $request->subject_id)
                          ->where('date', $date->format('Y-m-d'));
                }])
                ->orderBy('last_name')
                ->orderBy('first_name')
                ->get();

            // Get attendance statistics
            $totalStudents = $students->count();
            $presentCount = $students->filter(function ($student) {
                return $student->attendance->isNotEmpty() &&
                       in_array($student->attendance->first()->status, [AttendanceStatus::PRESENT, AttendanceStatus::LATE]);
            })->count();

            $absentCount = $totalStudents - $presentCount;
            $lateCount = $students->filter(function ($student) {
                return $student->attendance->isNotEmpty() &&
                       $student->attendance->first()->status === AttendanceStatus::LATE;
            })->count();

            $attendanceData = [
                'date' => $date->format('Y-m-d'),
                'grade_level' => $request->grade_level,
                'section' => $request->section,
                'subject_id' => $request->subject_id,
                'students' => $students,
                'statistics' => [
                    'total_students' => $totalStudents,
                    'present_count' => $presentCount,
                    'absent_count' => $absentCount,
                    'late_count' => $lateCount,
                    'attendance_rate' => $totalStudents > 0 ? round(($presentCount / $totalStudents) * 100, 2) : 0,
                ],
                'last_updated' => now()->format('Y-m-d H:i:s'),
            ];

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'data' => $attendanceData,
                ]);
            }

            return view('attendance.class', compact('attendanceData'));

        } catch (\Exception $e) {
            Log::error('Failed to get class attendance', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to retrieve class attendance',
                    'error' => $e->getMessage(),
                ], 500);
            }

            return back()->with('error', 'Failed to retrieve class attendance: ' . $e->getMessage());
        }
    }

    /**
     * Update existing attendance record.
     */
    public function updateAttendance(Request $request, Attendance $attendance): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'status' => ['required', 'in:' . implode(',', AttendanceStatus::values())],
                'time_in' => ['nullable', 'date_format:H:i:s'],
                'time_out' => ['nullable', 'date_format:H:i:s', 'after:time_in'],
                'remarks' => ['nullable', 'string', 'max:1000'],
                'reason' => ['required', 'string', 'max:500'], // Reason for update
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            // Store original data for audit
            $originalData = $attendance->toArray();

            DB::beginTransaction();

            $attendance->update([
                'status' => AttendanceStatus::from($request->status),
                'time_in' => $request->time_in,
                'time_out' => $request->time_out,
                'remarks' => $request->remarks,
            ]);

            // Log the update for audit trail
            Log::info('Attendance record updated', [
                'attendance_id' => $attendance->id,
                'student_id' => $attendance->student_id,
                'updated_by' => Auth::id(),
                'reason' => $request->reason,
                'original_data' => $originalData,
                'new_data' => $attendance->fresh()->toArray(),
            ]);

            DB::commit();

            // Broadcast real-time update
            $this->broadcastAttendanceUpdate($attendance);

            return response()->json([
                'success' => true,
                'message' => 'Attendance updated successfully',
                'data' => [
                    'attendance' => $attendance->load(['student', 'teacher', 'subject']),
                ],
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to update attendance', [
                'attendance_id' => $attendance->id,
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update attendance',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate SF2/SF4 forms for date range.
     */
    public function generateReport(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'report_type' => ['required', 'in:sf2,sf4,daily,weekly,monthly'],
                'grade_level' => ['required', 'in:11,12'],
                'section' => ['required', 'string', 'max:50'],
                'subject_id' => ['required', 'integer', 'exists:subjects,id'],
                'date_from' => ['required', 'date'],
                'date_to' => ['required', 'date', 'after_or_equal:date_from'],
                'format' => ['nullable', 'in:pdf,excel,csv'],
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $reportData = $this->reportService->generateReport([
                'report_type' => $request->report_type,
                'grade_level' => $request->grade_level,
                'section' => $request->section,
                'subject_id' => $request->subject_id,
                'date_from' => $request->date_from,
                'date_to' => $request->date_to,
                'format' => $request->format ?? 'pdf',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Report generated successfully',
                'data' => [
                    'report_path' => $reportData['path'],
                    'download_url' => Storage::url($reportData['path']),
                    'report_type' => $request->report_type,
                    'generated_at' => now()->format('Y-m-d H:i:s'),
                    'parameters' => $request->only(['grade_level', 'section', 'date_from', 'date_to']),
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to generate attendance report', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get attendance statistics and trends.
     */
    public function getAnalytics(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'grade_level' => ['nullable', 'in:11,12'],
                'section' => ['nullable', 'string', 'max:50'],
                'subject_id' => ['nullable', 'integer', 'exists:subjects,id'],
                'student_id' => ['nullable', 'integer', 'exists:students,id'],
                'date_from' => ['nullable', 'date'],
                'date_to' => ['nullable', 'date', 'after_or_equal:date_from'],
                'period' => ['nullable', 'in:daily,weekly,monthly,quarterly'],
                'include_trends' => ['nullable', 'boolean'],
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $dateFrom = $request->date_from ? Carbon::parse($request->date_from) : now()->subDays(30);
            $dateTo = $request->date_to ? Carbon::parse($request->date_to) : now();

            // Build base query
            $query = Attendance::with(['student', 'subject'])
                ->byDateRange($dateFrom->format('Y-m-d'), $dateTo->format('Y-m-d'));

            // Apply filters
            if ($request->filled('grade_level') || $request->filled('section')) {
                $query->whereHas('student', function ($q) use ($request) {
                    if ($request->filled('grade_level')) {
                        $q->byGradeLevel($request->grade_level);
                    }
                    if ($request->filled('section')) {
                        $q->bySection($request->section);
                    }
                });
            }

            if ($request->filled('subject_id')) {
                $query->where('subject_id', $request->subject_id);
            }

            if ($request->filled('student_id')) {
                $query->where('student_id', $request->student_id);
            }

            $attendanceRecords = $query->get();

            // Calculate analytics
            $analytics = [
                'summary' => [
                    'total_records' => $attendanceRecords->count(),
                    'present_count' => $attendanceRecords->where('status', AttendanceStatus::PRESENT)->count(),
                    'absent_count' => $attendanceRecords->where('status', AttendanceStatus::ABSENT)->count(),
                    'late_count' => $attendanceRecords->where('status', AttendanceStatus::LATE)->count(),
                    'excused_count' => $attendanceRecords->where('status', AttendanceStatus::EXCUSED)->count(),
                ],
                'rates' => [
                    'attendance_rate' => $attendanceRecords->count() > 0 ?
                        round(($attendanceRecords->whereIn('status', [AttendanceStatus::PRESENT, AttendanceStatus::LATE])->count() / $attendanceRecords->count()) * 100, 2) : 0,
                    'punctuality_rate' => $attendanceRecords->count() > 0 ?
                        round(($attendanceRecords->where('status', AttendanceStatus::PRESENT)->count() / $attendanceRecords->count()) * 100, 2) : 0,
                ],
                'period' => [
                    'date_from' => $dateFrom->format('Y-m-d'),
                    'date_to' => $dateTo->format('Y-m-d'),
                    'days_covered' => $dateFrom->diffInDays($dateTo) + 1,
                ],
            ];

            // Add trends if requested
            if ($request->boolean('include_trends')) {
                $analytics['trends'] = $this->calculateAttendanceTrends($attendanceRecords, $request->period ?? 'daily');
            }

            // Add student-specific analytics if student_id provided
            if ($request->filled('student_id')) {
                $student = Student::find($request->student_id);
                $analytics['student'] = [
                    'id' => $student->id,
                    'name' => $student->full_name,
                    'grade_level' => $student->grade_level,
                    'section' => $student->section,
                    'is_at_risk' => $student->isAtRisk(),
                    'attendance_rate' => $student->getAttendanceRate($dateFrom->diffInDays($dateTo) + 1),
                ];
            }

            return response()->json([
                'success' => true,
                'data' => $analytics,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get attendance analytics', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve analytics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Trigger SMS notification to parents.
     */
    public function sendSMSNotification(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'student_ids' => ['required', 'array', 'min:1'],
                'student_ids.*' => ['integer', 'exists:students,id'],
                'message_type' => ['required', 'in:absent,late,present,custom'],
                'custom_message' => ['required_if:message_type,custom', 'string', 'max:160'],
                'date' => ['nullable', 'date'],
                'subject_id' => ['nullable', 'integer', 'exists:subjects,id'],
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $date = $request->date ? Carbon::parse($request->date) : today();
            $results = [];
            $successCount = 0;
            $failureCount = 0;

            foreach ($request->student_ids as $studentId) {
                try {
                    $student = Student::find($studentId);

                    if (!$student || !$student->parent_phone) {
                        $results[] = [
                            'student_id' => $studentId,
                            'success' => false,
                            'message' => 'Student not found or no parent phone',
                        ];
                        $failureCount++;
                        continue;
                    }

                    // Get attendance record if needed
                    $attendance = null;
                    if ($request->filled('subject_id')) {
                        $attendance = Attendance::where('student_id', $studentId)
                            ->where('subject_id', $request->subject_id)
                            ->where('date', $date->format('Y-m-d'))
                            ->first();
                    }

                    // Build message
                    $message = $this->buildSMSMessage($request->message_type, $student, $attendance, $request->custom_message);

                    // Send SMS
                    $sent = $this->smsService->send($student->formatted_phone, $message);

                    $results[] = [
                        'student_id' => $studentId,
                        'student_name' => $student->full_name,
                        'phone' => $student->formatted_phone,
                        'success' => $sent,
                        'message' => $sent ? 'SMS sent successfully' : 'Failed to send SMS',
                    ];

                    if ($sent) {
                        $successCount++;
                    } else {
                        $failureCount++;
                    }

                } catch (\Exception $e) {
                    $results[] = [
                        'student_id' => $studentId,
                        'success' => false,
                        'message' => 'Error: ' . $e->getMessage(),
                    ];
                    $failureCount++;
                }
            }

            return response()->json([
                'success' => true,
                'message' => "SMS notifications processed: {$successCount} sent, {$failureCount} failed",
                'data' => [
                    'results' => $results,
                    'summary' => [
                        'total' => count($request->student_ids),
                        'success_count' => $successCount,
                        'failure_count' => $failureCount,
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send SMS notifications', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send SMS notifications',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Mark attendance for multiple students (bulk operation).
     */
    public function bulkAttendance(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'students' => ['required', 'array', 'min:1'],
                'students.*.student_id' => ['required', 'integer', 'exists:students,id'],
                'students.*.status' => ['required', 'in:' . implode(',', AttendanceStatus::values())],
                'students.*.time_in' => ['nullable', 'date_format:H:i:s'],
                'students.*.time_out' => ['nullable', 'date_format:H:i:s'],
                'students.*.remarks' => ['nullable', 'string', 'max:1000'],
                'teacher_id' => ['required', 'integer', 'exists:teachers,id'],
                'subject_id' => ['required', 'integer', 'exists:subjects,id'],
                'date' => ['required', 'date', 'before_or_equal:today'],
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $results = [];
            $successCount = 0;
            $failureCount = 0;

            DB::beginTransaction();

            foreach ($request->students as $studentData) {
                try {
                    $student = Student::find($studentData['student_id']);

                    if ($student->status->value !== 'active') {
                        $results[] = [
                            'student_id' => $studentData['student_id'],
                            'success' => false,
                            'message' => 'Student is not active',
                        ];
                        $failureCount++;
                        continue;
                    }

                    // Check for existing attendance
                    $existingAttendance = Attendance::where('student_id', $studentData['student_id'])
                        ->where('subject_id', $request->subject_id)
                        ->where('date', $request->date)
                        ->first();

                    if ($existingAttendance) {
                        // Update existing
                        $existingAttendance->update([
                            'status' => AttendanceStatus::from($studentData['status']),
                            'time_in' => $studentData['time_in'] ?? $existingAttendance->time_in,
                            'time_out' => $studentData['time_out'] ?? $existingAttendance->time_out,
                            'remarks' => $studentData['remarks'] ?? $existingAttendance->remarks,
                            'teacher_id' => $request->teacher_id,
                        ]);

                        $attendance = $existingAttendance;
                        $action = 'updated';
                    } else {
                        // Create new
                        $attendance = Attendance::create([
                            'student_id' => $studentData['student_id'],
                            'teacher_id' => $request->teacher_id,
                            'subject_id' => $request->subject_id,
                            'date' => $request->date,
                            'time_in' => $studentData['time_in'],
                            'time_out' => $studentData['time_out'],
                            'status' => AttendanceStatus::from($studentData['status']),
                            'remarks' => $studentData['remarks'],
                            'ip_address' => $request->ip(),
                            'device_info' => [
                                'user_agent' => $request->userAgent(),
                                'scan_method' => 'bulk',
                                'marked_by' => Auth::id(),
                            ],
                        ]);

                        $action = 'created';
                    }

                    // Cache for offline sync
                    $this->cacheAttendanceForOfflineSync($attendance);

                    $results[] = [
                        'student_id' => $studentData['student_id'],
                        'student_name' => $student->full_name,
                        'success' => true,
                        'message' => "Attendance {$action} successfully",
                        'attendance_id' => $attendance->id,
                    ];

                    $successCount++;

                } catch (\Exception $e) {
                    $results[] = [
                        'student_id' => $studentData['student_id'],
                        'success' => false,
                        'message' => 'Error: ' . $e->getMessage(),
                    ];
                    $failureCount++;
                }
            }

            DB::commit();

            // Broadcast bulk update
            $this->broadcastBulkAttendanceUpdate($request->subject_id, $request->date);

            return response()->json([
                'success' => true,
                'message' => "Bulk attendance processed: {$successCount} successful, {$failureCount} failed",
                'data' => [
                    'results' => $results,
                    'summary' => [
                        'total' => count($request->students),
                        'success_count' => $successCount,
                        'failure_count' => $failureCount,
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Bulk attendance operation failed', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process bulk attendance',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get student attendance history with filters.
     */
    public function getAttendanceHistory(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'student_id' => ['required', 'integer', 'exists:students,id'],
                'subject_id' => ['nullable', 'integer', 'exists:subjects,id'],
                'date_from' => ['nullable', 'date'],
                'date_to' => ['nullable', 'date', 'after_or_equal:date_from'],
                'status' => ['nullable', 'in:' . implode(',', AttendanceStatus::values())],
                'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $student = Student::find($request->student_id);
            $query = $student->attendance()->with(['teacher', 'subject']);

            // Apply filters
            if ($request->filled('subject_id')) {
                $query->where('subject_id', $request->subject_id);
            }

            if ($request->filled('date_from')) {
                $query->where('date', '>=', $request->date_from);
            }

            if ($request->filled('date_to')) {
                $query->where('date', '<=', $request->date_to);
            }

            if ($request->filled('status')) {
                $query->where('status', AttendanceStatus::from($request->status));
            }

            $perPage = $request->per_page ?? 20;
            $attendanceHistory = $query->orderBy('date', 'desc')
                ->orderBy('created_at', 'desc')
                ->paginate($perPage);

            // Calculate summary statistics
            $totalQuery = clone $query;
            $totalRecords = $totalQuery->count();

            $summary = [
                'total_records' => $totalRecords,
                'present_count' => (clone $query)->where('status', AttendanceStatus::PRESENT)->count(),
                'absent_count' => (clone $query)->where('status', AttendanceStatus::ABSENT)->count(),
                'late_count' => (clone $query)->where('status', AttendanceStatus::LATE)->count(),
                'excused_count' => (clone $query)->where('status', AttendanceStatus::EXCUSED)->count(),
            ];

            $summary['attendance_rate'] = $totalRecords > 0 ?
                round((($summary['present_count'] + $summary['late_count']) / $totalRecords) * 100, 2) : 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'student' => [
                        'id' => $student->id,
                        'name' => $student->full_name,
                        'student_id' => $student->student_id,
                        'grade_level' => $student->grade_level,
                        'section' => $student->section,
                    ],
                    'attendance_history' => $attendanceHistory,
                    'summary' => $summary,
                    'filters_applied' => $request->only(['subject_id', 'date_from', 'date_to', 'status']),
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get attendance history', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve attendance history',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export attendance data in various formats.
     */
    public function exportReport(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'export_type' => ['required', 'in:attendance,summary,analytics,sf2,sf4'],
                'format' => ['required', 'in:excel,csv,pdf'],
                'grade_level' => ['nullable', 'in:11,12'],
                'section' => ['nullable', 'string', 'max:50'],
                'subject_id' => ['nullable', 'integer', 'exists:subjects,id'],
                'student_id' => ['nullable', 'integer', 'exists:students,id'],
                'date_from' => ['required', 'date'],
                'date_to' => ['required', 'date', 'after_or_equal:date_from'],
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $exportData = $this->exportService->exportAttendanceData([
                'export_type' => $request->export_type,
                'format' => $request->format,
                'grade_level' => $request->grade_level,
                'section' => $request->section,
                'subject_id' => $request->subject_id,
                'student_id' => $request->student_id,
                'date_from' => $request->date_from,
                'date_to' => $request->date_to,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Export completed successfully',
                'data' => [
                    'file_path' => $exportData['path'],
                    'download_url' => Storage::url($exportData['path']),
                    'file_name' => $exportData['filename'],
                    'file_size' => $exportData['size'],
                    'export_type' => $request->export_type,
                    'format' => $request->format,
                    'generated_at' => now()->format('Y-m-d H:i:s'),
                    'parameters' => $request->only(['grade_level', 'section', 'subject_id', 'student_id', 'date_from', 'date_to']),
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to export attendance data', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to export attendance data',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    // HELPER METHODS

    /**
     * Check if student is late based on time.
     */
    protected function isStudentLate(string $timeIn): bool
    {
        // School policy: Classes start at 8:00 AM, late after 8:15 AM
        $lateThreshold = Carbon::today()->setTime(8, 15, 0);
        $timeInCarbon = Carbon::today()->setTimeFromTimeString($timeIn);

        return $timeInCarbon->gt($lateThreshold);
    }

    /**
     * Broadcast real-time attendance update.
     */
    protected function broadcastAttendanceUpdate(Attendance $attendance): void
    {
        try {
            // Using Laravel's broadcasting system
            event(new AttendanceRecorded($attendance));

            // Additional real-time update via cache for immediate UI updates
            $dateString = $attendance->date instanceof Carbon ? $attendance->date->format('Y-m-d') : $attendance->date;
            Cache::put(
                "attendance_update_{$attendance->subject_id}_{$dateString}",
                [
                    'attendance_id' => $attendance->id,
                    'student_id' => $attendance->student_id,
                    'status' => $attendance->status->value,
                    'time_in' => $attendance->time_in,
                    'time_out' => $attendance->time_out,
                    'updated_at' => now()->toISOString(),
                ],
                300 // 5 minutes
            );
        } catch (\Exception $e) {
            Log::warning('Failed to broadcast attendance update', [
                'attendance_id' => $attendance->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Broadcast bulk attendance update.
     */
    protected function broadcastBulkAttendanceUpdate(int $subjectId, string $date): void
    {
        try {
            Cache::put(
                "bulk_attendance_update_{$subjectId}_{$date}",
                [
                    'subject_id' => $subjectId,
                    'date' => $date,
                    'updated_at' => now()->toISOString(),
                ],
                300 // 5 minutes
            );
        } catch (\Exception $e) {
            Log::warning('Failed to broadcast bulk attendance update', [
                'subject_id' => $subjectId,
                'date' => $date,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Cache attendance for offline sync capability.
     */
    protected function cacheAttendanceForOfflineSync(Attendance $attendance): void
    {
        try {
            $cacheKey = "offline_sync_attendance_{$attendance->id}";
            $cacheData = [
                'id' => $attendance->id,
                'student_id' => $attendance->student_id,
                'teacher_id' => $attendance->teacher_id,
                'subject_id' => $attendance->subject_id,
                'date' => $attendance->date instanceof Carbon ? $attendance->date->format('Y-m-d') : $attendance->date,
                'time_in' => $attendance->time_in,
                'time_out' => $attendance->time_out,
                'status' => $attendance->status->value,
                'remarks' => $attendance->remarks,
                'synced' => true,
                'cached_at' => now()->toISOString(),
            ];

            Cache::put($cacheKey, $cacheData, 86400); // 24 hours

            // Add to sync queue for offline devices
            $syncQueue = Cache::get('offline_sync_queue', []);
            $syncQueue[] = $cacheKey;
            Cache::put('offline_sync_queue', array_unique($syncQueue), 86400);

        } catch (\Exception $e) {
            Log::warning('Failed to cache attendance for offline sync', [
                'attendance_id' => $attendance->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Build SMS message based on type and context.
     */
    protected function buildSMSMessage(string $messageType, Student $student, ?Attendance $attendance = null, ?string $customMessage = null): string
    {
        $schoolName = config('app.name', 'School');
        $studentName = $student->first_name;
        $date = now()->format('M d, Y');
        $time = now()->format('h:i A');

        return match($messageType) {
            'absent' => "Hi! {$studentName} was marked ABSENT today ({$date}). Please contact the school if this is incorrect. - {$schoolName}",
            'late' => "Hi! {$studentName} arrived LATE today ({$date}) at {$time}. Please remind about punctuality. - {$schoolName}",
            'present' => "Hi! {$studentName} arrived safely at school today ({$date}) at {$time}. - {$schoolName}",
            'custom' => $customMessage ?? "Message from {$schoolName} regarding {$studentName}.",
            default => "Attendance update for {$studentName} on {$date}. - {$schoolName}",
        };
    }

    /**
     * Calculate attendance trends for analytics.
     */
    protected function calculateAttendanceTrends(Collection $attendanceRecords, string $period): array
    {
        $trends = [];

        try {
            $groupedData = match($period) {
                'daily' => $attendanceRecords->groupBy(fn($record) => $record->date->format('Y-m-d')),
                'weekly' => $attendanceRecords->groupBy(fn($record) => $record->date->format('Y-W')),
                'monthly' => $attendanceRecords->groupBy(fn($record) => $record->date->format('Y-m')),
                default => $attendanceRecords->groupBy(fn($record) => $record->date->format('Y-m-d')),
            };

            foreach ($groupedData as $periodKey => $records) {
                $total = $records->count();
                $present = $records->where('status', AttendanceStatus::PRESENT)->count();
                $late = $records->where('status', AttendanceStatus::LATE)->count();
                $absent = $records->where('status', AttendanceStatus::ABSENT)->count();

                $trends[] = [
                    'period' => $periodKey,
                    'total' => $total,
                    'present' => $present,
                    'late' => $late,
                    'absent' => $absent,
                    'attendance_rate' => $total > 0 ? round((($present + $late) / $total) * 100, 2) : 0,
                ];
            }

            // Sort by period
            usort($trends, fn($a, $b) => strcmp($a['period'], $b['period']));

        } catch (\Exception $e) {
            Log::warning('Failed to calculate attendance trends', [
                'period' => $period,
                'error' => $e->getMessage(),
            ]);
        }

        return $trends;
    }
}
